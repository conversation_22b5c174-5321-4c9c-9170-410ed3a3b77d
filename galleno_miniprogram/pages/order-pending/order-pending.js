// order-pending.js
const api = require('../../utils/api.js')
const PaymentUtils = require('../../utils/payment.js')

Page({
  data: {
    orderData: {},
    loading: false
  },

  onLoad: function (options) {
    console.log('订单待支付页面加载，参数:', options);

    // 从参数或本地存储获取订单数据
    let orderData = null;

    if (options.orderData) {
      // 从页面参数获取
      try {
        orderData = JSON.parse(decodeURIComponent(options.orderData));
      } catch (e) {
        console.error('解析订单数据失败:', e);
      }
    }

    if (!orderData) {
      // 从本地存储获取
      orderData = wx.getStorageSync('currentOrder');
    }

    if (orderData) {
      // 确保订单状态为待支付
      orderData.status = 'pending';
      orderData.paymentStatus = 'unpaid';

      // 确保必要的字段存在
      orderData = this.normalizeOrderData(orderData);

      this.setData({
        orderData: orderData
      });

      // 更新本地存储
      wx.setStorageSync('currentOrder', orderData);
    } else {
      // 如果没有订单数据，返回首页
      wx.showModal({
        title: '提示',
        content: '订单信息丢失，请重新预订',
        showCancel: false,
        success: () => {
          this.backToHome();
        }
      });
    }
  },

  // 标准化订单数据
  normalizeOrderData: function (orderData) {
    console.log('标准化订单数据:', orderData);

    // 确保必要字段存在
    const normalized = {
      orderId: orderData.orderId || orderData.id,
      orderNo: orderData.orderNo || orderData.orderNumber || this.generateOrderId(),
      conferenceId: orderData.conferenceId,
      conferenceTitle: orderData.conferenceTitle || orderData.conferenceName || '会议酒店预订',
      categoryId: orderData.categoryId,
      roomId: orderData.roomId,
      roomName: orderData.roomName || orderData.roomTitle || orderData.roomType || '标准房',
      roomType: orderData.roomType || orderData.roomName,
      checkinDate: orderData.checkinDate || orderData.checkInDate,
      checkoutDate: orderData.checkoutDate || orderData.checkOutDate,
      nights: orderData.nights || this.calculateNights(orderData.checkinDate, orderData.checkoutDate),
      depositAmount: orderData.depositAmount || orderData.deposit || 200,
      totalAmount: orderData.totalAmount || orderData.totalPrice || orderData.depositAmount || 200,
      status: 'pending',
      paymentStatus: 'unpaid',
      guestInfo: orderData.guestInfo || {
        name: orderData.guestName || '',
        phone: orderData.guestPhone || '',
        idCard: orderData.guestIdCard || ''
      },
      createTime: orderData.createTime || orderData.createdAt || new Date().toISOString(),
      orderTime: orderData.orderTime || new Date().toISOString()
    };

    console.log('标准化后的订单数据:', normalized);
    return normalized;
  },

  // 计算住宿天数
  calculateNights: function (checkinDate, checkoutDate) {
    if (!checkinDate || !checkoutDate) return 1;

    const checkin = new Date(checkinDate);
    const checkout = new Date(checkoutDate);

    if (isNaN(checkin.getTime()) || isNaN(checkout.getTime())) return 1;

    const diffTime = checkout.getTime() - checkin.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return Math.max(1, diffDays);
  },

  // 生成订单ID
  generateOrderId: function () {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000);
    return `ORDER${timestamp}${random}`;
  },

  onShow: function () {
    console.log('订单待支付页面显示');
  },

  // 重新支付
  retryPayment: function () {
    if (this.data.loading) {
      return;
    }

    const orderData = this.data.orderData;
    if (!orderData) {
      wx.showToast({
        title: '订单信息异常',
        icon: 'none'
      });
      return;
    }

    // 检查必要的支付信息
    if (!orderData.conferenceId || !orderData.roomId) {
      wx.showModal({
        title: '订单信息不完整',
        content: '订单缺少必要的预订信息，请重新预订',
        showCancel: false,
        confirmText: '重新预订',
        success: () => {
          this.backToHome();
        }
      });
      return;
    }

    this.setData({ loading: true });

    // 构建支付请求数据
    const createOrderData = {
      conferenceId: orderData.conferenceId,
      categoryId: orderData.categoryId,
      roomId: orderData.roomId,
      checkinDate: orderData.checkinDate,
      checkoutDate: orderData.checkoutDate,
      nights: orderData.nights,
      guestName: orderData.guestInfo?.name || '',
      guestPhone: orderData.guestInfo?.phone || '',
      guestIdCard: orderData.guestInfo?.idCard || '',
      totalAmount: orderData.depositAmount || 200,
      depositAmount: orderData.depositAmount || 200
    };

    console.log('重新发起支付，订单数据:', createOrderData);

    // 调用支付工具类创建订单并支付
    PaymentUtils.createPayment(createOrderData)
      .then((res) => {
        console.log('重新支付成功', res);
        this.handlePaymentSuccess(orderData);
      })
      .catch((err) => {
        console.log('重新支付失败', err);
        this.handlePaymentFail(err);
      })
      .finally(() => {
        this.setData({ loading: false });
      });
  },

  // 支付成功处理
  handlePaymentSuccess: function (orderData) {
    // 更新订单状态
    orderData.status = 'paid';
    orderData.paymentStatus = 'paid';
    orderData.paymentTime = new Date().toISOString();
    
    // 保存到本地存储
    wx.setStorageSync('currentOrder', orderData);
    
    // 添加到订单历史
    let orderHistory = wx.getStorageSync('orderHistory') || [];
    orderHistory.unshift(orderData);
    wx.setStorageSync('orderHistory', orderHistory);

    // 跳转到支付成功页面
    wx.redirectTo({
      url: '/pages/payment-success/payment-success'
    });
  },

  // 支付失败处理
  handlePaymentFail: function (error) {
    if (error.message === '用户取消支付') {
      // 用户再次取消支付，保持在当前页面
      wx.showToast({
        title: '支付已取消',
        icon: 'none'
      });
    } else {
      // 其他支付错误
      wx.showModal({
        title: '支付失败',
        content: '支付过程中出现问题，请重试或联系客服',
        showCancel: true,
        cancelText: '重试',
        confirmText: '联系客服',
        success: (res) => {
          if (res.confirm) {
            this.contactService();
          } else if (res.cancel) {
            // 用户选择重试
            this.retryPayment();
          }
        }
      });
    }
  },

  // 取消订单
  cancelOrder: function () {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消这个订单吗？取消后需要重新预订。',
      success: (res) => {
        if (res.confirm) {
          // 清除订单数据
          wx.removeStorageSync('currentOrder');
          
          wx.showToast({
            title: '订单已取消',
            icon: 'success'
          });
          
          // 返回首页
          setTimeout(() => {
            this.backToHome();
          }, 1500);
        }
      }
    });
  },

  // 联系客服
  contactService: function () {
    wx.makePhoneCall({
      phoneNumber: '4008889999',
      fail: () => {
        wx.showToast({
          title: '拨打失败',
          icon: 'none'
        });
      }
    });
  },

  // 返回首页
  backToHome: function () {
    wx.switchTab({
      url: '/pages/conference-list/conference-list'
    });
  },

  // 格式化日期
  formatDate: function (dateString) {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    return `${month}-${day}`;
  },

  // 格式化时间
  formatTime: function (timeString) {
    if (!timeString) return '';
    
    const date = new Date(timeString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  },

  // 分享功能
  onShareAppMessage: function () {
    return {
      title: '会议酒店预定 - 订单待支付',
      path: '/pages/conference-list/conference-list'
    };
  }
});
